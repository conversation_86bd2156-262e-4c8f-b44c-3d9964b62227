<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Debug logging
        \Log::info('AdminMiddleware: Request received', [
            'url' => $request->url(),
            'method' => $request->method(),
            'headers' => $request->headers->all(),
            'user' => $request->user() ? $request->user()->toArray() : null
        ]);

        if (!$request->user()) {
            \Log::warning('AdminMiddleware: No authenticated user');
            return response()->json([
                'success' => false,
                'error' => 'Authentication required'
            ], 401);
        }

        if (!$request->user()->isAdmin()) {
            \Log::warning('AdminMiddleware: User is not admin', [
                'user_id' => $request->user()->id,
                'role' => $request->user()->role
            ]);
            return response()->json([
                'success' => false,
                'error' => 'Admin access required'
            ], 403);
        }

        \Log::info('AdminMiddleware: Access granted');
        return $next($request);
    }
}
