const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('adminToken');
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  if (!response.ok) {
    if (response.status === 401) {
      // Token expired or invalid - redirect to login
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      window.location.href = '/admin/login';
      throw new Error('Authentication required');
    }
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
};

// API service object
export const api = {
  // GET request
  get: async (endpoint, options = {}) => {
    const token = getAuthToken();
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    return { data: await handleResponse(response) };
  },

  // POST request
  post: async (endpoint, data = null, options = {}) => {
    const token = getAuthToken();
    const config = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...(data && { body: JSON.stringify(data) }),
      ...options,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    return { data: await handleResponse(response) };
  },

  // PUT request
  put: async (endpoint, data = null, options = {}) => {
    const token = getAuthToken();
    const config = {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...(data && { body: JSON.stringify(data) }),
      ...options,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    return { data: await handleResponse(response) };
  },

  // DELETE request
  delete: async (endpoint, options = {}) => {
    const token = getAuthToken();
    const config = {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    return { data: await handleResponse(response) };
  },

  // PATCH request
  patch: async (endpoint, data = null, options = {}) => {
    const token = getAuthToken();
    const config = {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...(data && { body: JSON.stringify(data) }),
      ...options,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    return { data: await handleResponse(response) };
  },

  // File upload helper
  upload: async (endpoint, formData, options = {}) => {
    const token = getAuthToken();
    const config = {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` }),
        // Don't set Content-Type for FormData - let browser set it with boundary
        ...options.headers,
      },
      body: formData,
      ...options,
    };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    return { data: await handleResponse(response) };
  }
};

export default api;
