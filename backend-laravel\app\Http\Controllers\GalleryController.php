<?php

namespace App\Http\Controllers;

use App\Models\Gallery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class GalleryController extends Controller
{
    /**
     * Get all gallery items (public)
     */
    public function index(Request $request)
    {
        $query = Gallery::with('uploader:id,name')
            ->active()
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc');

        // Category filter
        if ($request->has('category') && $request->category !== 'all') {
            $query->category($request->category);
        }

        // Featured filter
        if ($request->has('featured') && $request->boolean('featured')) {
            $query->featured();
        }

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        $galleries = $query->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => $galleries->items(),
            'pagination' => [
                'current_page' => $galleries->currentPage(),
                'last_page' => $galleries->lastPage(),
                'per_page' => $galleries->perPage(),
                'total' => $galleries->total(),
            ]
        ]);
    }

    /**
     * Get single gallery item by ID (public)
     */
    public function show($id)
    {
        $gallery = Gallery::with('uploader:id,name')
            ->active()
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $gallery
        ]);
    }

    /**
     * Get all gallery items for admin (including inactive)
     */
    public function adminIndex(Request $request)
    {
        $query = Gallery::with('uploader:id,name')
            ->orderBy('created_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Category filter
        if ($request->has('category') && $request->category !== 'all') {
            $query->category($request->category);
        }

        // Status filter
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Featured filter
        if ($request->has('featured')) {
            if ($request->featured === 'true') {
                $query->featured();
            } elseif ($request->featured === 'false') {
                $query->where('featured', false);
            }
        }

        $galleries = $query->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $galleries->items(),
            'pagination' => [
                'current_page' => $galleries->currentPage(),
                'last_page' => $galleries->lastPage(),
                'per_page' => $galleries->perPage(),
                'total' => $galleries->total(),
            ]
        ]);
    }

    /**
     * Store new gallery item (admin only)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'category' => 'required|in:Kegiatan Rutin,Kegiatan Khusus,Prestasi,Fasilitas,Ekstrakurikuler',
            'featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        // Handle file upload
        $imageUrl = null;
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = time() . '_' . $image->getClientOriginalName();
            $path = $image->storeAs('gallery', $filename, 'public');
            $imageUrl = '/storage/' . $path;
        }

        $gallery = Gallery::create([
            'title' => $request->title,
            'description' => $request->description,
            'image_url' => $imageUrl,
            'category' => $request->category,
            'uploaded_by' => auth()->id(),
            'featured' => $request->boolean('featured', false),
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->get('sort_order', 0),
        ]);

        $gallery->load('uploader:id,name');

        return response()->json([
            'success' => true,
            'message' => 'Gallery item created successfully',
            'data' => $gallery
        ], 201);
    }

    /**
     * Update gallery item (admin only)
     */
    public function update(Request $request, $id)
    {
        $gallery = Gallery::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'category' => 'required|in:Kegiatan Rutin,Kegiatan Khusus,Prestasi,Fasilitas,Ekstrakurikuler',
            'featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        $updateData = [
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'featured' => $request->boolean('featured', false),
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->get('sort_order', 0),
        ];

        // Handle file upload if new image provided
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($gallery->image_url) {
                $oldPath = str_replace('/storage/', '', $gallery->image_url);
                Storage::disk('public')->delete($oldPath);
            }

            $image = $request->file('image');
            $filename = time() . '_' . $image->getClientOriginalName();
            $path = $image->storeAs('gallery', $filename, 'public');
            $updateData['image_url'] = '/storage/' . $path;
        }

        $gallery->update($updateData);
        $gallery->load('uploader:id,name');

        return response()->json([
            'success' => true,
            'message' => 'Gallery item updated successfully',
            'data' => $gallery
        ]);
    }

    /**
     * Delete gallery item (admin only)
     */
    public function destroy($id)
    {
        $gallery = Gallery::findOrFail($id);

        // Delete image file if exists
        if ($gallery->image_url) {
            $path = str_replace('/storage/', '', $gallery->image_url);
            Storage::disk('public')->delete($path);
        }

        $gallery->delete();

        return response()->json([
            'success' => true,
            'message' => 'Gallery item deleted successfully'
        ]);
    }
}
