import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import PageTransition from '../ui/PageTransition';
import { SidebarProvider, useSidebar } from '../../hooks/useSidebar.jsx';
import { isTokenExpired, clearAuthTokens } from '../../hooks/useAdminAuth';

const AdminLayoutContent = ({ children }) => {
  const navigate = useNavigate();
  const { sidebarWidth, setIsMobileOpen } = useSidebar();

  useEffect(() => {
    // Check JWT token authentication
    const token = localStorage.getItem('adminToken');
    const userData = localStorage.getItem('adminUser');

    if (!token || !userData) {
      clearAuthTokens();
      navigate('/admin/login', { replace: true });
      return;
    }

    // Check if token is expired
    if (isTokenExpired()) {
      clearAuthTokens();
      navigate('/admin/login', { replace: true });
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (!parsedUser || parsedUser.role !== 'admin') {
        clearAuthTokens();
        navigate('/admin/login', { replace: true });
      }
    } catch (error) {
      console.error('Error parsing user data:', error);
      clearAuthTokens();
      navigate('/admin/login', { replace: true });
    }
  }, [navigate]);

  return (
    <div className="flex h-screen bg-gray-100 overflow-hidden">
      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 bg-white shadow-md z-30 px-4 py-3">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setIsMobileOpen(true)}
            className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition duration-300"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Admin Panel</h1>
          <div className="w-10"></div> {/* Spacer for centering */}
        </div>
      </div>

      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <main
        className="flex-1 flex flex-col overflow-hidden transition-all duration-300"
        style={{ marginLeft: `${sidebarWidth}px` }}
      >
        <div className="lg:hidden h-16 flex-shrink-0"></div> {/* Spacer for mobile header */}
        <div className="flex-1 overflow-y-auto">
          <PageTransition>
            {children}
          </PageTransition>
        </div>
      </main>
    </div>
  );
};

const AdminLayout = ({ children }) => {
  return (
    <SidebarProvider>
      <AdminLayoutContent>{children}</AdminLayoutContent>
    </SidebarProvider>
  );
};

export default AdminLayout;
