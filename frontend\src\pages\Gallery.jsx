import React, { useState, useEffect } from 'react';
import ImageSlider from '../components/ImageSlider';
import ImageModal from '../components/ImageModal';
import { useGallery, useFeaturedGallery } from '../hooks/useGallery';
import { useSchoolSettings } from '../hooks/useSchoolSettings';

const Gallery = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Use hooks for data fetching
  const { images, loading, error } = useGallery(selectedCategory);
  const { images: featuredImages, loading: featuredLoading } = useFeaturedGallery();
  const { settings, loading: settingsLoading } = useSchoolSettings();

  const categories = ['all', 'Kegiatan Rutin', 'Kegiatan Khusus', 'Prestasi', 'Fasilitas', 'Ekstrakurikuler'];

  const openModal = (image, index = 0) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setSelectedImage(null);
  };

  const goToNextImage = () => {
    const nextIndex = currentImageIndex === images.length - 1 ? 0 : currentImageIndex + 1;
    setCurrentImageIndex(nextIndex);
    setSelectedImage(images[nextIndex]);
  };

  const goToPrevImage = () => {
    const prevIndex = currentImageIndex === 0 ? images.length - 1 : currentImageIndex - 1;
    setCurrentImageIndex(prevIndex);
    setSelectedImage(images[prevIndex]);
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          Galeri Kegiatan
        </h1>
        <p className="text-xl text-gray-600">
          {settingsLoading ? 'Loading...' : `Dokumentasi kegiatan dan fasilitas ${settings.schoolName}`}
        </p>
      </div>

      {/* Featured Images Slider */}
      {!featuredLoading && featuredImages.length > 0 && (
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Sorotan Kegiatan</h2>
          <ImageSlider
            images={featuredImages}
            onImageClick={(image, index) => openModal(image, index)}
          />
        </div>
      )}

      {/* No Featured Images Message */}
      {!featuredLoading && featuredImages.length === 0 && (
        <div className="mb-12 text-center py-8 bg-gray-50 rounded-lg">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Belum Ada Galeri Unggulan</h3>
          <p className="text-gray-600">Galeri unggulan akan ditampilkan di sini setelah ditambahkan oleh admin.</p>
        </div>
      )}

      {/* Category Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 justify-center">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-lg font-semibold transition duration-300 ${
                selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category === 'all' ? 'Semua' : category}
            </button>
          ))}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Images Grid */}
      {!loading && (
        <>
          {images.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
              {images.map((image, index) => (
                <div
                  key={image.id}
                  className="group relative bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300 cursor-pointer"
                  onClick={() => openModal(image, index)}
                >
                  <div className="aspect-w-4 aspect-h-3 relative">
                    <img
                      src={image.url || image.image_url || image.path || '/placeholder-image.jpg'}
                      alt={image.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition duration-300"
                      onError={(e) => {
                        e.target.src = '/placeholder-image.jpg';
                      }}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition duration-300"></div>
                    
                    {/* Overlay Info */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 opacity-0 group-hover:opacity-100 transition duration-300">
                      <h3 className="text-white font-semibold text-sm mb-1 line-clamp-2">
                        {image.title}
                      </h3>
                      <p className="text-gray-300 text-xs">
                        {image.category}
                      </p>
                    </div>

                    {/* Zoom Icon */}
                    <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-2 opacity-0 group-hover:opacity-100 transition duration-300">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16 bg-gray-50 rounded-lg">
              <svg className="w-20 h-20 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Belum Ada Galeri</h3>
              <p className="text-gray-600 mb-6">
                Galeri kegiatan dan fasilitas sekolah akan ditampilkan di sini setelah ditambahkan oleh admin.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-blue-800 text-sm">
                  <strong>Info:</strong> Admin dapat menambahkan foto kegiatan melalui panel admin untuk ditampilkan di galeri ini.
                </p>
              </div>
            </div>
          )}
        </>
      )}

      {/* Image Modal */}
      <ImageModal
        isOpen={modalOpen}
        onClose={closeModal}
        image={selectedImage}
        images={images}
        currentIndex={currentImageIndex}
        onNext={goToNextImage}
        onPrev={goToPrevImage}
      />
    </div>
  );
};

export default Gallery;
