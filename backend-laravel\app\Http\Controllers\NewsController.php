<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NewsController extends Controller
{
    /**
     * Get published news (public)
     */
    public function index(Request $request)
    {
        $query = News::with('author:id,name')
            ->published()
            ->orderBy('published_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Featured filter
        if ($request->has('featured') && $request->featured) {
            $query->featured();
        }

        // Pagination
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        
        $news = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'total' => $news->total(),
            'page' => $news->currentPage(),
            'pages' => $news->lastPage(),
        ]);
    }

    /**
     * Get single news by ID (public)
     */
    public function show($id)
    {
        $news = News::with('author:id,name')
            ->published()
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $news
        ]);
    }

    /**
     * Get all news for admin (including unpublished)
     */
    public function adminIndex(Request $request)
    {
        $query = News::with('author:id,name')
            ->orderBy('created_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Status filter
        if ($request->has('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->where('published', false);
            }
        }

        // Pagination
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        
        $news = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'total' => $news->total(),
            'page' => $news->currentPage(),
            'pages' => $news->lastPage(),
        ]);
    }

    /**
     * Create new news (admin only)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'image_url' => 'nullable|string',
            'published' => 'boolean',
            'featured' => 'boolean',
            'tags' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()->first()
            ], 400);
        }

        $news = News::create([
            'title' => $request->title,
            'content' => $request->content,
            'excerpt' => $request->excerpt,
            'image_url' => $request->image_url,
            'author_id' => $request->user()->id,
            'published' => $request->boolean('published', false),
            'featured' => $request->boolean('featured', false),
            'tags' => $request->tags ?? [],
            'published_at' => $request->boolean('published') ? now() : null,
        ]);

        $news->load('author:id,name');

        return response()->json([
            'success' => true,
            'message' => 'News created successfully',
            'data' => $news
        ], 201);
    }

    /**
     * Update news (admin only)
     */
    public function update(Request $request, $id)
    {
        $news = News::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'content' => 'sometimes|required|string',
            'excerpt' => 'nullable|string',
            'image_url' => 'nullable|string',
            'published' => 'boolean',
            'featured' => 'boolean',
            'tags' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()->first()
            ], 400);
        }

        $updateData = $request->only(['title', 'content', 'excerpt', 'image_url', 'tags']);
        
        if ($request->has('published')) {
            $updateData['published'] = $request->boolean('published');
            if ($updateData['published'] && !$news->published_at) {
                $updateData['published_at'] = now();
            }
        }
        
        if ($request->has('featured')) {
            $updateData['featured'] = $request->boolean('featured');
        }

        $news->update($updateData);
        $news->load('author:id,name');

        return response()->json([
            'success' => true,
            'message' => 'News updated successfully',
            'data' => $news
        ]);
    }

    /**
     * Delete news (admin only)
     */
    public function destroy($id)
    {
        $news = News::findOrFail($id);
        $news->delete();

        return response()->json([
            'success' => true,
            'message' => 'News deleted successfully'
        ]);
    }
}
