import { useState, useEffect } from 'react';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export const useGallery = (category = 'all', featured = false) => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchGallery = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Build query parameters
        const params = new URLSearchParams();
        if (category && category !== 'all') {
          params.append('category', category);
        }
        if (featured) {
          params.append('featured', 'true');
        }
        
        const url = `${API_BASE_URL}/gallery${params.toString() ? `?${params.toString()}` : ''}`;
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
          setImages(data.data || []);
        } else {
          throw new Error(data.message || 'Failed to fetch gallery');
        }
      } catch (err) {
        console.error('Error fetching gallery:', err);
        setError(err.message);
        setImages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchGallery();
  }, [category, featured]);

  return { images, loading, error };
};

export const useFeaturedGallery = () => {
  return useGallery('all', true);
};
