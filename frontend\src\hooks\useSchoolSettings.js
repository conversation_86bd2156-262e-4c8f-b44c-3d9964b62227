import { useState, useEffect } from 'react';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

const defaultSettings = {
  schoolName: 'SMA Negeri 1 Jakarta',
  schoolShortName: 'SMAN 1 Jakarta',
  schoolAddress: 'Jl. Pendidikan No. 123, Menteng, Jakarta Pusat, DKI Jakarta 10310',
  schoolPhone: '021-12345678',
  schoolEmail: '<EMAIL>',
  schoolWebsite: 'https://www.sman1jakarta.sch.id',
  principalName: 'Dr. <PERSON>, M.Pd',
  schoolMotto: 'Unggul dalam Prestasi, Berkarakter, dan <PERSON>',
  schoolDescription: 'SMA Negeri 1 Jakarta adalah sekolah menengah atas negeri yang berkomitmen untuk memberikan pendidikan berkualitas tinggi dengan mengembangkan potensi akademik dan karakter siswa.',
  logoUrl: '/images/logo-school.png'
};

export const useSchoolSettings = () => {
  const [settings, setSettings] = useState(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fetch settings from API
    const fetchSettings = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`${API_BASE_URL}/settings`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
          const apiSettings = { ...defaultSettings, ...result.data };
          setSettings(apiSettings);

          // Cache in localStorage
          localStorage.setItem('schoolSettings', JSON.stringify(apiSettings));
        } else {
          throw new Error('Failed to fetch settings');
        }
      } catch (err) {
        console.error('Error fetching school settings:', err);
        setError(err.message);

        // Try to load from localStorage as fallback
        try {
          const savedSettings = localStorage.getItem('schoolSettings');
          if (savedSettings) {
            const parsedSettings = JSON.parse(savedSettings);
            setSettings({ ...defaultSettings, ...parsedSettings });
          } else {
            setSettings(defaultSettings);
          }
        } catch (localError) {
          console.error('Error loading cached settings:', localError);
          setSettings(defaultSettings);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();

    // Listen for settings updates
    const handleSettingsUpdate = (event) => {
      if (event.detail) {
        setSettings({ ...defaultSettings, ...event.detail });
      }
    };

    window.addEventListener('schoolSettingsUpdated', handleSettingsUpdate);

    return () => {
      window.removeEventListener('schoolSettingsUpdated', handleSettingsUpdate);
    };
  }, []);

  const updateSettings = (newSettings) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));
    
    // Dispatch event to notify other components
    window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', { 
      detail: updatedSettings 
    }));
  };

  return {
    settings,
    loading,
    error,
    updateSettings
  };
};

export default useSchoolSettings;
