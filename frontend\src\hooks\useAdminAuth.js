import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// JWT Token utilities
const TOKEN_KEY = 'adminToken';
const USER_KEY = 'adminUser';
const TOKEN_EXPIRY_KEY = 'adminTokenExpiry';

const isTokenExpired = () => {
  const expiry = localStorage.getItem(TOKEN_EXPIRY_KEY);
  if (!expiry) return true;
  return Date.now() > parseInt(expiry);
};

const setTokenWithExpiry = (token, user, expiryHours = 24) => {
  const expiry = Date.now() + (expiryHours * 60 * 60 * 1000); // 24 hours default
  localStorage.setItem(TOKEN_KEY, token);
  localStorage.setItem(USER_KEY, JSON.stringify(user));
  localStorage.setItem(TOKEN_EXPIRY_KEY, expiry.toString());
};

const clearAuthTokens = () => {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(USER_KEY);
  localStorage.removeItem(TOKEN_EXPIRY_KEY);
};

export const useAdminAuth = () => {
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [authError, setAuthError] = useState(null);

  // Check if user is authenticated with JWT validation
  const checkAuth = () => {
    const token = localStorage.getItem(TOKEN_KEY);
    const userData = localStorage.getItem(USER_KEY);

    if (!token || !userData) {
      clearAuthTokens();
      return false;
    }

    // Check if token is expired
    if (isTokenExpired()) {
      clearAuthTokens();
      setAuthError('Sesi login Anda telah berakhir. Silakan login kembali.');
      return false;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (!parsedUser || parsedUser.role !== 'admin') {
        clearAuthTokens();
        return false;
      }
      setUser(parsedUser);
      return true;
    } catch (error) {
      console.error('Error parsing user data:', error);
      clearAuthTokens();
      return false;
    }
  };

  // Handle authentication error with immediate redirect
  const handleAuthError = (message = 'Anda harus login sebagai admin terlebih dahulu') => {
    setAuthError(message);
    setIsAuthenticated(false);
    setUser(null);

    // Clear all auth tokens
    clearAuthTokens();

    // Immediate redirect to login (no delay for better UX)
    navigate('/admin/login', { replace: true });
  };

  // Handle API errors
  const handleApiError = (error) => {
    if (error.message.includes('Authentication required') || 
        error.message.includes('401') ||
        error.message.includes('Unauthenticated')) {
      handleAuthError('Sesi login Anda telah berakhir. Silakan login kembali.');
      return true;
    }
    
    if (error.message.includes('403') || error.message.includes('Forbidden')) {
      handleAuthError('Anda tidak memiliki akses untuk melakukan tindakan ini.');
      return true;
    }
    
    return false;
  };

  // Logout function with complete cleanup
  const logout = () => {
    clearAuthTokens();
    setIsAuthenticated(false);
    setUser(null);
    setAuthError(null);
    navigate('/admin/login', { replace: true });
  };

  // Initialize authentication check with auto redirect
  useEffect(() => {
    const initAuth = () => {
      setIsLoading(true);

      if (checkAuth()) {
        setIsAuthenticated(true);
        setAuthError(null);
      } else {
        // Auto redirect to login if not authenticated
        setIsAuthenticated(false);
        handleAuthError();
      }

      setIsLoading(false);
    };

    initAuth();

    // Set up token expiry check interval (check every 5 minutes)
    const tokenCheckInterval = setInterval(() => {
      if (isTokenExpired()) {
        handleAuthError('Sesi login Anda telah berakhir. Silakan login kembali.');
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(tokenCheckInterval);
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Clear auth error
  const clearAuthError = () => {
    setAuthError(null);
  };

  // Force login redirect
  const redirectToLogin = () => {
    navigate('/admin/login', { replace: true });
  };

  // Login function to set tokens
  const login = (token, userData, expiryHours = 24) => {
    setTokenWithExpiry(token, userData, expiryHours);
    setUser(userData);
    setIsAuthenticated(true);
    setAuthError(null);
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    authError,
    checkAuth,
    handleAuthError,
    handleApiError,
    logout,
    login,
    clearAuthError,
    redirectToLogin
  };
};

// Export JWT utilities for use in login component
export { setTokenWithExpiry, clearAuthTokens, isTokenExpired };

// Re-export AuthErrorComponent for backward compatibility
export { default as AuthErrorComponent } from '../components/auth/AuthErrorComponent';
