import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// JWT Token utilities
const TOKEN_KEY = 'adminToken';
const USER_KEY = 'adminUser';
const TOKEN_EXPIRY_KEY = 'adminTokenExpiry';

const isTokenExpired = () => {
  const expiry = localStorage.getItem(TOKEN_EXPIRY_KEY);
  if (!expiry) return true;
  return Date.now() > parseInt(expiry);
};

const setTokenWithExpiry = (token, user, expiryHours = 24) => {
  const expiry = Date.now() + (expiryHours * 60 * 60 * 1000); // 24 hours default
  localStorage.setItem(TOKEN_KEY, token);
  localStorage.setItem(USER_KEY, JSON.stringify(user));
  localStorage.setItem(TOKEN_EXPIRY_KEY, expiry.toString());
};

const clearAuthTokens = () => {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(USER_KEY);
  localStorage.removeItem(TOKEN_EXPIRY_KEY);
};

export const useAdminAuth = () => {
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [authError, setAuthError] = useState(null);

  // Check if user is authenticated with JWT validation
  const checkAuth = () => {
    const token = localStorage.getItem(TOKEN_KEY);
    const userData = localStorage.getItem(USER_KEY);

    if (!token || !userData) {
      clearAuthTokens();
      return false;
    }

    // Check if token is expired
    if (isTokenExpired()) {
      clearAuthTokens();
      setAuthError('Sesi login Anda telah berakhir. Silakan login kembali.');
      return false;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (!parsedUser || parsedUser.role !== 'admin') {
        clearAuthTokens();
        return false;
      }
      setUser(parsedUser);
      return true;
    } catch (error) {
      console.error('Error parsing user data:', error);
      clearAuthTokens();
      return false;
    }
  };

  // Handle authentication error with immediate redirect
  const handleAuthError = (message = 'Anda harus login sebagai admin terlebih dahulu') => {
    setAuthError(message);
    setIsAuthenticated(false);
    setUser(null);

    // Clear all auth tokens
    clearAuthTokens();

    // Immediate redirect to login (no delay for better UX)
    navigate('/admin/login', { replace: true });
  };

  // Handle API errors
  const handleApiError = (error) => {
    if (error.message.includes('Authentication required') || 
        error.message.includes('401') ||
        error.message.includes('Unauthenticated')) {
      handleAuthError('Sesi login Anda telah berakhir. Silakan login kembali.');
      return true;
    }
    
    if (error.message.includes('403') || error.message.includes('Forbidden')) {
      handleAuthError('Anda tidak memiliki akses untuk melakukan tindakan ini.');
      return true;
    }
    
    return false;
  };

  // Logout function with complete cleanup
  const logout = () => {
    clearAuthTokens();
    setIsAuthenticated(false);
    setUser(null);
    setAuthError(null);
    navigate('/admin/login', { replace: true });
  };

  // Initialize authentication check with auto redirect
  useEffect(() => {
    const initAuth = () => {
      setIsLoading(true);

      if (checkAuth()) {
        setIsAuthenticated(true);
        setAuthError(null);
      } else {
        // Auto redirect to login if not authenticated
        setIsAuthenticated(false);
        handleAuthError();
      }

      setIsLoading(false);
    };

    initAuth();

    // Set up token expiry check interval (check every 5 minutes)
    const tokenCheckInterval = setInterval(() => {
      if (isTokenExpired()) {
        handleAuthError('Sesi login Anda telah berakhir. Silakan login kembali.');
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(tokenCheckInterval);
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Clear auth error
  const clearAuthError = () => {
    setAuthError(null);
  };

  // Force login redirect
  const redirectToLogin = () => {
    navigate('/admin/login', { replace: true });
  };

  // Login function to set tokens
  const login = (token, userData, expiryHours = 24) => {
    setTokenWithExpiry(token, userData, expiryHours);
    setUser(userData);
    setIsAuthenticated(true);
    setAuthError(null);
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    authError,
    checkAuth,
    handleAuthError,
    handleApiError,
    logout,
    login,
    clearAuthError,
    redirectToLogin
  };
};

// Export JWT utilities for use in login component
export { setTokenWithExpiry, clearAuthTokens, isTokenExpired };

// Component for displaying authentication error
export const AuthErrorComponent = ({ authError, clearAuthError, redirectToLogin }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Akses Ditolak</h2>
          <p className="text-gray-600 mb-6">
            {authError || 'Anda harus login sebagai admin untuk mengakses halaman ini.'}
          </p>
          <div className="space-y-3">
            <button
              onClick={redirectToLogin}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-200"
            >
              Login Sekarang
            </button>
            <button
              onClick={clearAuthError}
              className="w-full text-gray-500 hover:text-gray-700 transition duration-200"
            >
              Tutup
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
