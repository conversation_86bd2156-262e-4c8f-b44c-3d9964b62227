import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

export const useAdminAuth = () => {
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [authError, setAuthError] = useState(null);

  // Check if user is authenticated
  const checkAuth = () => {
    const token = localStorage.getItem('adminToken');
    const userData = localStorage.getItem('adminUser');
    
    if (!token || !userData) {
      return false;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (!parsedUser || parsedUser.role !== 'admin') {
        return false;
      }
      setUser(parsedUser);
      return true;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return false;
    }
  };

  // Handle authentication error
  const handleAuthError = (message = 'Anda harus login sebagai admin terlebih dahulu') => {
    setAuthError(message);
    setIsAuthenticated(false);
    
    // Clear invalid tokens
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    
    // Redirect to login after 2 seconds
    setTimeout(() => {
      navigate('/admin/login');
    }, 2000);
  };

  // Handle API errors
  const handleApiError = (error) => {
    if (error.message.includes('Authentication required') || 
        error.message.includes('401') ||
        error.message.includes('Unauthenticated')) {
      handleAuthError('Sesi login Anda telah berakhir. Silakan login kembali.');
      return true;
    }
    
    if (error.message.includes('403') || error.message.includes('Forbidden')) {
      handleAuthError('Anda tidak memiliki akses untuk melakukan tindakan ini.');
      return true;
    }
    
    return false;
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    setIsAuthenticated(false);
    setUser(null);
    navigate('/admin/login');
  };

  // Initialize authentication check
  useEffect(() => {
    setIsLoading(true);
    
    if (checkAuth()) {
      setIsAuthenticated(true);
      setAuthError(null);
    } else {
      handleAuthError();
    }
    
    setIsLoading(false);
  }, [navigate]);

  // Clear auth error
  const clearAuthError = () => {
    setAuthError(null);
  };

  // Force login redirect
  const redirectToLogin = () => {
    navigate('/admin/login');
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    authError,
    checkAuth,
    handleAuthError,
    handleApiError,
    logout,
    clearAuthError,
    redirectToLogin
  };
};

// Higher-order component for protecting admin routes
export const withAdminAuth = (WrappedComponent) => {
  return function AuthenticatedComponent(props) {
    const { isAuthenticated, isLoading, authError, clearAuthError, redirectToLogin } = useAdminAuth();

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Akses Ditolak</h2>
              <p className="text-gray-600 mb-6">
                {authError || 'Anda harus login sebagai admin untuk mengakses halaman ini.'}
              </p>
              <div className="space-y-3">
                <button
                  onClick={redirectToLogin}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-200"
                >
                  Login Sekarang
                </button>
                <button
                  onClick={clearAuthError}
                  className="w-full text-gray-500 hover:text-gray-700 transition duration-200"
                >
                  Tutup
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };
};
